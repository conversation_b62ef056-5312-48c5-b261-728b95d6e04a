import { JsonObject } from 'type-fest'
import { Injectable, Logger } from '@nestjs/common'
import { Op } from '@infra-node/sequelize'
import { get, set, cloneDeep } from 'lodash'

import { DatabaseService } from '../../database/database.service'
import { MaterialSmartDescription } from '../../database/models'
import { ModelManager } from '../../database/models'
import { ForwardMaterialPlatformService } from '../forward/material-platform/material-platform.service'
import { fetchFromCDN } from '../../tools/cdn'

// 物料 Schema 接口定义
interface MaterialSchema {
  componentName: string
  componentChineseName?: string
  [key: string]: unknown
}

@Injectable()
export class MaterialSmartDescriptionService {
  private readonly logger = new Logger(MaterialSmartDescriptionService.name)

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly materialPlatformService: ForwardMaterialPlatformService,
  ) {}

  async createSmartDescription(params: {
    jobId: number
    materialId: number
    materialPubId: number
    materialVersion: string
    result: Service.MaterialSmartDescription.BasicMaterialDescription
    namespace: string
    schemaUrl: string
  }): Promise<MaterialSmartDescription> {
    const now = Date.now()
    const uuid = ModelManager.genPrimaryIndex()
    const models = this.databaseService.getModels()

    const description = await models.MaterialSmartDescription.create({
      id: uuid,
      materialId: params.materialId,
      materialPubId: params.materialPubId,
      materialVersion: params.materialVersion,
      jobId: params.jobId,
      smartDescription: params.result as unknown as JsonObject,
      createTime: now,
      state: 1,
      namespace: params.namespace,
      schemaUrl: params.schemaUrl,
      publishStatus: 1, // FIXME: 默认为草稿状态
    })
    return description.toJSON() as unknown as MaterialSmartDescription
  }

  /**
   * 根据 ID 查找描述
   */
  async findDescriptionById(
    id: number,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findByPk(id)
      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`查找描述失败 ID: ${id}`, error)
      throw error
    }
  }

  /**
   * 根据物料 ID 查找描述
   */
  async findDescriptionsByMaterialId(
    materialId: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialId,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找描述失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据物料版本查找描述
   */
  async findDescriptionsByMaterialVersion(
    materialVersion: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialVersion,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料版本查找描述失败 materialVersion: ${materialVersion}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据作业 ID 查找描述
   */
  async findDescriptionByJobId(
    jobId: number,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findOne({
        where: {
          jobId,
          state: 1, // 只查询正常状态的记录
        },
      })
      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`根据作业ID查找描述失败 jobId: ${jobId}`, error)
      throw error
    }
  }

  /**
   * 获取所有描述（支持分页和过滤）
   */
  async findAllDescriptions(
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          state: 1, // 只查询正常状态的记录
        },
        limit,
        offset,
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error('获取描述列表失败', error)
      throw error
    }
  }

  /**
   * 更新智能描述内容
   */
  async updateDescriptionContent(
    id: number,
    smartDescription: Service.MaterialSmartDescription.BasicMaterialDescription,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findByPk(id)

      if (!description) {
        return null
      }

      await description.update({
        smartDescription: smartDescription as unknown as JsonObject,
      })
      this.logger.log(`智能描述内容更新成功: ${description.id}`)
      return description.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`更新智能描述内容失败 ID: ${id}`, error)
      throw error
    }
  }

  /**
   * 更新描述状态
   */
  async updateDescriptionState(
    id: number,
    state: number,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findByPk(id)

      if (!description) {
        return null
      }

      await description.update({ state })
      this.logger.log(`描述状态更新成功: ${description.id}, 新状态: ${state}`)
      return description.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`更新描述状态失败 ID: ${id}`, error)
      throw error
    }
  }

  /**
   * 根据物料 ID 和版本查找最新的有效描述
   */
  async findLatestDescriptionByMaterial(
    materialId: number,
    materialVersion?: string,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        materialId,
        state: 1, // 只查询正常状态的记录
      }

      if (materialVersion) {
        whereCondition.materialVersion = materialVersion
      }

      const description = await models.MaterialSmartDescription.findOne({
        where: whereCondition,
        order: [['createTime', 'DESC']],
      })

      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`查找最新描述失败 materialId: ${materialId}`, error)
      throw error
    }
  }

  /**
   * 根据物料 ID 查找待处理描述
   */
  async findPendingDescriptionsByMaterialId(
    materialId: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          materialId,
          state: 0, // 只查询待处理状态的记录
        },
        limit,
        offset,
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据物料ID查找待处理描述失败 materialId: ${materialId}`,
        error,
      )
      throw error
    }
  }

  /**
   * 执行原生 SQL 查询
   */
  async executeRawQuery(sql: string): Promise<unknown> {
    try {
      const result = await this.databaseService.query(sql)
      this.logger.log('执行原生 SQL 查询成功')
      return result
    }
    catch (error) {
      this.logger.error('执行原生 SQL 查询失败', error)
      throw error
    }
  }

  /**
   * 更新 ZhiDa 描述内容
   * 通过创建新记录的方式实现更新，保留历史版本
   */
  async updateZhidaDescription(
    params: Service.MaterialSmartDescription.UpdateZhidaDescriptionParams,
  ): Promise<Service.MaterialSmartDescription.UpdateZhidaDescriptionResult> {
    try {
      const { id, updateDto } = params

      // 查找原始记录
      const originalDescription = await this.findDescriptionById(id)
      if (!originalDescription) {
        return {
          success: false,
          error: '描述不存在',
        }
      }

      // 深拷贝现有的 smartDescription 以避免修改原始数据
      const updatedSmartDescription = cloneDeep(
        originalDescription.smartDescription,
      ) as unknown as Service.MaterialSmartDescription.BasicMaterialDescription

      // 更新对应的字段
      if (updateDto.description !== undefined) {
        set(
          updatedSmartDescription,
          'exportType.explanation',
          updateDto.description,
        )
      }

      if (updateDto.propsDefine !== undefined) {
        set(
          updatedSmartDescription,
          'api.parameters.typescriptCode',
          updateDto.propsDefine,
        )
      }

      if (updateDto.jsxDemo !== undefined) {
        set(updatedSmartDescription, 'usage.basicExample', updateDto.jsxDemo)
      }

      // 更新 payload 中的字段
      const payload = get(
        updatedSmartDescription,
        'payload',
        {},
      ) as Partial<Service.MaterialSmartDescription.MaterialUserDefine>

      if (updateDto.childNested !== undefined) {
        payload.childNested = updateDto.childNested
      }

      if (updateDto.jsxPropCompatible !== undefined) {
        payload.jsxPropCompatible = updateDto.jsxPropCompatible
      }

      if (updateDto.mergePropsBeforeInsert !== undefined) {
        payload.mergePropsBeforeInsert = updateDto.mergePropsBeforeInsert
      }

      if (updateDto.purePropEffect !== undefined) {
        payload.purePropEffect = updateDto.purePropEffect
      }

      // 将更新后的 payload 设置回 smartDescription
      set(updatedSmartDescription, 'payload', payload)

      // 创建新记录而不是更新现有记录
      const newDescription = await this.createSmartDescriptionFromExisting({
        originalDescription,
        updatedSmartDescription,
      })

      if (!newDescription) {
        return {
          success: false,
          error: '创建新记录失败',
        }
      }

      return {
        success: true,
        data: newDescription as unknown as Service.MaterialSmartDescription.EnrichedDescription,
      }
    }
    catch (error) {
      this.logger.error(`更新 ZhiDa 描述失败 ID: ${params.id}`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      }
    }
  }

  /**
   * 基于现有记录创建新的智能描述记录
   * 保留原有记录的基本信息，但使用新的 smartDescription 和 createTime
   */
  async createSmartDescriptionFromExisting(params: {
    originalDescription: MaterialSmartDescription
    updatedSmartDescription: Service.MaterialSmartDescription.BasicMaterialDescription
  }): Promise<MaterialSmartDescription | null> {
    try {
      const { originalDescription, updatedSmartDescription } = params
      const now = Date.now()
      const uuid = ModelManager.genPrimaryIndex()
      const models = this.databaseService.getModels()

      // 从对应的 Job 表获取最新的 schemaUrl
      const job = await models.MaterialSmartDescriptionJob.findByPk(originalDescription.jobId)
      const schemaUrl = job?.schemaUrl || originalDescription.schemaUrl

      const newDescription = await models.MaterialSmartDescription.create({
        id: uuid,
        materialId: originalDescription.materialId,
        materialPubId: originalDescription.materialPubId,
        materialVersion: originalDescription.materialVersion,
        jobId: originalDescription.jobId, // 保持原有的 jobId
        smartDescription: updatedSmartDescription as unknown as JsonObject,
        createTime: now, // 使用当前时间作为新的创建时间
        state: 1, // 新记录状态为正常
        namespace: originalDescription.namespace, // 保持原有的 namespace
        schemaUrl, // 从 Job 表继承最新的 schemaUrl
        publishStatus: originalDescription.publishStatus, // 保持原有的发布状态
      })

      this.logger.log(
        `基于记录 ${originalDescription.id} 创建新的智能描述记录: ${newDescription.id}`,
      )
      return newDescription.toJSON() as unknown as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`创建新智能描述记录失败`, error)
      throw error
    }
  }

  /**
   * 根据物料标识参数查找最新的有效描述
   */
  async findLatestDescriptionByMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const whereCondition: Record<string, unknown> = {
        state: 1, // 只查询正常状态的记录
      }

      // 根据传入的参数构建查询条件
      if (params.materialId) {
        whereCondition.materialId = params.materialId
      }

      if (params.materialVersionName) {
        whereCondition.materialVersion = params.materialVersionName
      }

      // 如果提供了 namespace，可以直接通过 namespace 查询
      if (params.namespace) {
        whereCondition.namespace = params.namespace
      }

      const description = await models.MaterialSmartDescription.findOne({
        where: whereCondition,
        order: [['createTime', 'DESC']],
      })

      return description?.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error('根据物料标识参数查找最新描述失败', error)
      throw error
    }
  }

  /**
   * 根据 namespace 查找描述（支持模糊查询）
   */
  async findDescriptionsByNamespace(
    namespace: string,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          namespace: {
            [Op.like]: `%${namespace}%`, // 支持模糊查询
          },
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据 namespace 模糊查找描述失败 namespace: ${namespace}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据 namespace 统计描述数量（支持模糊查询）
   */
  async countDescriptionsByNamespace(namespace: string): Promise<number> {
    try {
      const models = this.databaseService.getModels()
      const count = await models.MaterialSmartDescription.count({
        where: {
          namespace: {
            [Op.like]: `%${namespace}%`, // 支持模糊查询
          },
          state: 1, // 只统计正常状态的记录
        },
      })
      return count
    }
    catch (error) {
      this.logger.error(
        `根据 namespace 统计描述数量失败 namespace: ${namespace}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据发布状态查找描述
   */
  async findDescriptionsByPublishStatus(
    publishStatus: number,
    limit: number = 10,
    offset: number = 0,
  ): Promise<MaterialSmartDescription[]> {
    try {
      const models = this.databaseService.getModels()
      const descriptions = await models.MaterialSmartDescription.findAll({
        where: {
          publishStatus,
          state: 1, // 只查询正常状态的记录
        },
        limit: Number(limit),
        offset: Number(offset),
        order: [['createTime', 'DESC']],
      })
      return descriptions.map(
        desc => desc.toJSON() as MaterialSmartDescription,
      )
    }
    catch (error) {
      this.logger.error(
        `根据发布状态查找描述失败 publishStatus: ${publishStatus}`,
        error,
      )
      throw error
    }
  }

  /**
   * 根据发布状态统计描述数量
   */
  async countDescriptionsByPublishStatus(publishStatus: number): Promise<number> {
    try {
      const models = this.databaseService.getModels()
      const count = await models.MaterialSmartDescription.count({
        where: {
          publishStatus,
          state: 1, // 只统计正常状态的记录
        },
      })
      return count
    }
    catch (error) {
      this.logger.error(
        `根据发布状态统计描述数量失败 publishStatus: ${publishStatus}`,
        error,
      )
      throw error
    }
  }

  /**
   * 更新发布状态
   */
  async updatePublishStatus(
    id: number,
    publishStatus: number,
  ): Promise<MaterialSmartDescription | null> {
    try {
      const models = this.databaseService.getModels()
      const description = await models.MaterialSmartDescription.findByPk(id)

      if (!description) {
        return null
      }

      await description.update({
        publishStatus,
      })
      this.logger.log(`发布状态更新成功: ${description.id} -> ${publishStatus}`)
      return description.toJSON() as MaterialSmartDescription
    }
    catch (error) {
      this.logger.error(`更新发布状态失败 ID: ${id}`, error)
      throw error
    }
  }

  /**
   * 构建 ZhiDa 描述对象
   * 将构建逻辑抽取为独立方法，便于复用
   */
  buildZhidaDescription(
    params: Service.MaterialSmartDescription.BuildZhidaDescriptionParams,
  ): Service.MaterialSmartDescription.ZhiDaNeededMaterialDescription {
    const { smartDescription, materialDetail } = params

    return {
      title: materialDetail.title,
      name: materialDetail.name,
      namespace: materialDetail.namespace,
      description: get(
        smartDescription,
        'exportType.explanation',
        '',
      ) as string,
      propsDefine: get(
        smartDescription,
        'api.parameters.typescriptCode',
        '',
      ) as string,
      jsxDemo: get(smartDescription, 'usage.basicExample', []) as string[],
      ...(get(
        smartDescription,
        'payload',
        {},
      ) as Partial<Service.MaterialSmartDescription.MaterialUserDefine>),
    }
  }

  /**
   * 从 CDN 获取物料 Schema 数据
   */
  async getSchemaFromCDN(schemaUrl: string): Promise<MaterialSchema | null> {
    try {
      if (!schemaUrl) {
        return null
      }

      this.logger.verbose(`从 CDN 获取 schema: ${schemaUrl}`)
      const schema = await fetchFromCDN<MaterialSchema>(schemaUrl)

      if (!schema || typeof schema !== 'object') {
        this.logger.warn(`Schema 数据格式不正确: ${schemaUrl}`)
        return null
      }

      return schema
    }
    catch (error) {
      this.logger.error(`从 CDN 获取 schema 失败: ${schemaUrl}`, error)
      return null
    }
  }

  /**
   * 从 Schema 数据中提取物料基础信息
   */
  extractMaterialDetailFromSchema(
    schema: MaterialSchema,
    namespace: string,
  ): Service.MaterialSmartDescription.MaterialDetailInfo {
    return {
      title: schema.componentChineseName || schema.componentName || '未知物料',
      name: schema.componentName || 'unknown',
      namespace: namespace || '未知',
    }
  }

  /**
   * 从描述记录获取物料基础信息（优先从 schema 获取）
   */
  async getMaterialDetailInfoFromDescription(
    description: MaterialSmartDescription,
  ): Promise<Service.MaterialSmartDescription.MaterialDetailInfo> {
    // 优先从 schemaUrl 获取
    if (description.schemaUrl) {
      try {
        const schema = await this.getSchemaFromCDN(description.schemaUrl)
        if (schema) {
          return this.extractMaterialDetailFromSchema(schema, description.namespace)
        }
      }
      catch (error) {
        this.logger.warn(`从 schema 获取物料信息失败，fallback 到原方法: ${error.message}`)
      }
    }

    // fallback 到原方法
    return this.getMaterialDetailInfo(description.materialId, description.materialVersion)
  }

  /**
   * 获取物料基础信息，包含错误处理和 fallback 逻辑
   */
  async getMaterialDetailInfo(
    materialId: number,
    materialVersion: string,
  ): Promise<Service.MaterialSmartDescription.MaterialDetailInfo> {
    try {
      const detail = await this.materialPlatformService.getMaterialVersionDetail({
        materialId,
        materialVersionName: materialVersion,
      })
      return {
        title: detail.title,
        name: detail.currentVersion.schema.componentName,
        namespace: detail.namespace,
      }
    }
    catch (error) {
      this.logger.error(`获取物料详情失败: ${materialId}`, error)
      return {
        title: '未知物料',
        name: 'unknown',
        namespace: '未知',
      }
    }
  }

  /**
   * 构建富化的描述对象
   */
  async buildEnrichedDescription(
    rawDescription: Service.MaterialSmartDescription.RawDescriptionQueryResult,
  ): Promise<Service.MaterialSmartDescription.EnrichedDescription> {
    // 构造 MaterialSmartDescription 对象以使用新的获取方法
    const descriptionObj = {
      id: rawDescription.id,
      materialId: rawDescription.material_id,
      materialVersion: rawDescription.material_version,
      materialPubId: rawDescription.material_pub_id,
      jobId: rawDescription.job_id,
      smartDescription: rawDescription.smart_description,
      createTime: rawDescription.create_time,
      state: rawDescription.state,
      namespace: rawDescription.namespace,
      schemaUrl: rawDescription.schema_url,
      publishStatus: rawDescription.publish_status,
    } as MaterialSmartDescription

    const materialDetail = await this.getMaterialDetailInfoFromDescription(descriptionObj)

    const zhidaDescription = this.buildZhidaDescription({
      smartDescription: rawDescription.smart_description,
      materialDetail,
    })

    return {
      id: rawDescription.id,
      materialId: rawDescription.material_id,
      materialVersion: rawDescription.material_version,
      materialPubId: rawDescription.material_pub_id,
      jobId: rawDescription.job_id,
      smartDescription: rawDescription.smart_description,
      createTime: rawDescription.create_time,
      state: rawDescription.state,
      materialDetail,
      zhidaDescription,
      namespace: rawDescription.namespace,
      schemaUrl: rawDescription.schema_url,
      publishStatus: rawDescription.publish_status,
    }
  }

  /**
   * 分页获取富化的物料智能描述列表
   */
  async getEnrichedDescriptions(
    params: Service.PaginationParams,
  ): Promise<Service.PaginationResult<Service.MaterialSmartDescription.EnrichedDescription>> {
    const { pageNum = 1, pageSize = 10 } = params
    const offset = (pageNum - 1) * pageSize

    // 使用兼容 MySQL 5.7 的查询方式，通过子查询获取每个 material_pub_id 的最新记录
    const sql = `
      SELECT d1.*
      FROM material_smart_description d1
      INNER JOIN (
        SELECT material_pub_id, MAX(create_time) as max_create_time
        FROM material_smart_description
        WHERE state = 1
        GROUP BY material_pub_id
      ) d2 ON d1.material_pub_id = d2.material_pub_id AND d1.create_time = d2.max_create_time
      WHERE d1.state = 1
      ORDER BY d1.create_time DESC
      LIMIT ${pageSize} OFFSET ${offset}
    `

    const countSql = `
      SELECT COUNT(DISTINCT material_pub_id) as total
      FROM material_smart_description
      WHERE state = 1
    `

    const [descriptions, countResult] = await Promise.all([
      this.executeRawQuery(sql),
      this.executeRawQuery(countSql),
    ])

    const total = (countResult as { total: number }[])[0]?.total || 0
    const totalPage = Math.ceil(total / pageSize)

    // 并行获取富化的描述信息
    const enrichedDescriptions = await Promise.all(
      (descriptions as Service.MaterialSmartDescription.RawDescriptionQueryResult[])
        .map(desc => this.buildEnrichedDescription(desc)),
    )

    return {
      list: enrichedDescriptions,
      total,
      pageNum,
      pageSize,
      totalPage,
      hasNext: pageNum < totalPage,
      hasPrev: pageNum > 1,
    }
  }

  /**
   * 根据 ID 获取富化的描述详情
   */
  async getEnrichedDescriptionById(
    id: number,
  ): Promise<Service.MaterialSmartDescription.EnrichedDescription | null> {
    const description = await this.findDescriptionById(id)
    if (!description) {
      return null
    }

    const materialDetail = await this.getMaterialDetailInfoFromDescription(description)

    const zhidaDescription = this.buildZhidaDescription({
      smartDescription: description.smartDescription,
      materialDetail,
    })

    return {
      ...description,
      materialDetail,
      zhidaDescription,
    }
  }

  /**
   * 通过物料标识参数查询物料的 zhidaDescription
   */
  async getDescriptionByMaterialIdentifier(
    params: Service.Forward.MaterialPlatform.MaterialIdentifierParams,
  ): Promise<Service.MaterialSmartDescription.ZhiDaNeededMaterialDescription | null> {
    // 验证参数
    ForwardMaterialPlatformService.assertMaterialIdentifier(params)

    let materialId = params.materialId
    const materialVersionName = params.materialVersionName!

    // 如果只提供了 namespace，需要先通过物料平台获取物料详情
    if (params.namespace && !params.materialId) {
      try {
        const materialDetail = await this.materialPlatformService.getMaterialVersionDetail(params)
        materialId = materialDetail.id
      }
      catch (error) {
        this.logger.error('通过 namespace 获取物料详情失败', error)
        return null
      }
    }

    if (!materialId) {
      throw new Error('无法确定物料 ID')
    }

    // 查找最新的描述记录
    const description = await this.findLatestDescriptionByMaterial(
      materialId,
      materialVersionName,
    )

    if (!description) {
      return null
    }

    // 获取物料基础信息
    const materialDetail = await this.getMaterialDetailInfoFromDescription(description)

    // 构建 zhidaDescription
    return this.buildZhidaDescription({
      smartDescription: description.smartDescription,
      materialDetail,
    })
  }

  /**
   * 更新 ZhiDa 描述并返回富化的结果
   */
  async updateZhidaDescriptionWithEnrichment(
    params: Service.MaterialSmartDescription.UpdateZhidaDescriptionParams,
  ): Promise<Service.MaterialSmartDescription.UpdateZhidaDescriptionResult> {
    const result = await this.updateZhidaDescription(params)

    if (!result.success || !result.data) {
      return result
    }

    // 获取物料基础信息并构建完整的响应
    const updatedDescription = result.data

    // 构造 MaterialSmartDescription 对象以使用新的获取方法
    const descriptionObj = {
      id: updatedDescription.id,
      materialId: updatedDescription.materialId,
      materialVersion: updatedDescription.materialVersion,
      materialPubId: updatedDescription.materialPubId,
      jobId: updatedDescription.jobId,
      smartDescription: updatedDescription.smartDescription,
      createTime: updatedDescription.createTime,
      state: updatedDescription.state,
      namespace: updatedDescription.namespace,
      schemaUrl: updatedDescription.schemaUrl,
      publishStatus: updatedDescription.publishStatus,
    } as MaterialSmartDescription

    const materialDetail = await this.getMaterialDetailInfoFromDescription(descriptionObj)

    // 构建 zhidaDescription
    const zhidaDescription = this.buildZhidaDescription({
      smartDescription: updatedDescription.smartDescription,
      materialDetail,
    })

    // 返回更新后的完整数据
    return {
      success: true,
      data: {
        ...updatedDescription,
        materialDetail,
        zhidaDescription,
      },
    }
  }
}
